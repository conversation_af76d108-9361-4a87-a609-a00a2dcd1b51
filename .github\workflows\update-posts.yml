name: Update Posts Index

on:
  push:
    branches:
      - main
    paths:
      - 'posts/*.md'
  workflow_dispatch:

jobs:
  update-index:
    runs-on: ubuntu-latest
    permissions: 
      contents: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Initialize package.json and install dependencies
        run: |
          npm init -y
          npm install gray-matter

      - name: Run posts scanner
        run: node scan-posts.js

      - name: Check for changes
        id: check_changes
        run: |
          git diff --exit-code posts/index.json || echo "index_changed=true" >> $GITHUB_OUTPUT

      - name: Commit and push if changed
        if: steps.check_changes.outputs.index_changed == 'true'
        run: |
          git config --local user.email "github-actions[bot]@users.noreply.github.com"
          git config --local user.name "github-actions[bot]"
          git add posts/index.json
          git commit -m "chore: update posts index [skip ci]"
          git push