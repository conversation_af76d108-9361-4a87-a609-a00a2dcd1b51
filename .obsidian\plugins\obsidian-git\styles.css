@keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.workspace-leaf-content[data-type="git-view"] .button-border {
    border: 2px solid var(--interactive-accent);
    border-radius: var(--radius-s);
}

.workspace-leaf-content[data-type="git-view"] .view-content {
    padding: 0;
}

.workspace-leaf-content[data-type="git-history-view"] .view-content {
    padding: 0;
}

.loading > svg {
    animation: 2s linear infinite loading;
    transform-origin: 50% 50%;
    display: inline-block;
}

.obsidian-git-center {
    margin: auto;
    text-align: center;
    width: 50%;
}

.obsidian-git-textarea {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.obsidian-git-disabled {
    opacity: 0.5;
}

.obsidian-git-center-button {
    display: block;
    margin: 20px auto;
}

.tooltip.mod-left {
    overflow-wrap: break-word;
}

.tooltip.mod-right {
    overflow-wrap: break-word;
}
.git-tools {
    display: flex;
    margin-left: auto;
}
.git-tools .type {
    padding-left: var(--size-2-1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
}

.git-tools .type[data-type="M"] {
    color: orange;
}
.git-tools .type[data-type="D"] {
    color: red;
}
.git-tools .buttons {
    display: flex;
}
.git-tools .buttons > * {
    padding: 0 0;
    height: auto;
}

.is-active .git-tools .buttons > * {
    color: var(--nav-item-color-active);
}

.git-author {
    color: var(--text-accent);
}

.git-date {
    color: var(--text-accent);
}

.git-ref {
    color: var(--text-accent);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
    display: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--interactive-accent);
    font-family: var(--font-monospace);
    height: 35px;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    font-size: 14px;
    margin-left: auto;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
    border: 1px solid #b4e2b4;
    border-radius: 5px 0 0 5px;
    color: #399839;
    padding: 2px;
    text-align: right;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
    border: 1px solid #e9aeae;
    border-radius: 0 5px 5px 0;
    color: #c33;
    margin-left: 1px;
    padding: 2px;
    text-align: left;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 15px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    margin-bottom: 1em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    display: none;
    font-size: 12px;
    justify-content: flex-end;
    padding: 4px 8px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
    background-color: #c8e1ff;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
    margin: 0 4px 0 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
    border-collapse: collapse;
    font-family: Menlo, Consolas, monospace;
    font-size: 13px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
    overflow-y: hidden;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
    display: inline-block;
    margin-bottom: -8px;
    margin-right: -4px;
    overflow-x: scroll;
    overflow-y: hidden;
    width: 50%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
    padding: 0 8em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    padding: 0 4.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
    word-wrap: normal;
    background: none;
    display: inline-block;
    padding: 0;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    vertical-align: middle;
    white-space: pre;
    width: 100%;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #ffb6ba;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #8d232881;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
    border-radius: 0.2em;
    display: inline-block;
    margin-top: -1px;
    text-decoration: none;
    vertical-align: middle;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #97f295;
    text-align: left;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #1d921996;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
    word-wrap: normal;
    background: none;
    display: inline;
    padding: 0;
    white-space: pre;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1 {
    float: left;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1,
.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 0.5em;
    text-overflow: ellipsis;
    width: 3.5em;
}

.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    float: right;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    position: absolute;
    text-align: right;
    width: 7.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    padding: 0 0.5em;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    width: 4em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
    position: relative;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    direction: rtl;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #fee8e9;
    border-color: #e9aeae;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: #dfd;
    border-color: #b4e2b4;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #521b1d83;
    border-color: #691d1d73;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: rgba(30, 71, 30, 0.5);
    border-color: #13501381;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-info {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
    color: var(--text-normal);
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #fdf2d0;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #55492480;
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: #ded;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: rgba(37, 78, 37, 0.418);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
    margin-bottom: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
    color: #3572b0;
    text-decoration: none;
}

.workspace-leaf-content[data-type="diff-view"]
    .d2h-file-list-wrapper
    a:visited {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
    font-weight: 700;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li {
    border-bottom: 1px solid var(--background-modifier-border);
    margin: 0;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li:last-child {
    border-bottom: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
    cursor: pointer;
    display: none;
    font-size: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-icon {
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
    color: #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added {
    color: #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed {
    color: #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-tag {
    background-color: var(--background-primary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    margin-left: 5px;
    padding: 0 2px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
    border: 2px solid #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
    border: 1px solid #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
    border: 1px solid #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
    border: 1px solid #3572b0;
}

/* ====================== Line Authoring Information ====================== */

.cm-gutterElement.obs-git-blame-gutter {
    /* Add background color to spacing inbetween and around the gutter for better aesthetics */
    border-width: 0px 2px 0.2px 2px;
    border-style: solid;
    border-color: var(--background-secondary);
    background-color: var(--background-secondary);
}

.cm-gutterElement.obs-git-blame-gutter > div,
.line-author-settings-preview {
    /* delegate text color to settings */
    color: var(--obs-git-gutter-text);
    font-family: monospace;
    height: 100%; /* ensure, that age-based background color occupies entire parent */
    text-align: right;
    padding: 0px 6px 0px 6px;
    white-space: pre; /* Keep spaces and do not collapse them. */
}

@media (max-width: 800px) {
    /* hide git blame gutter not to superpose text */
    .cm-gutterElement.obs-git-blame-gutter {
        display: none;
    }
}

.git-unified-diff-view,
.git-split-diff-view .cm-deletedLine .cm-changedText {
    background-color: #ee443330;
}

.git-unified-diff-view,
.git-split-diff-view .cm-insertedLine .cm-changedText {
    background-color: #22bb2230;
}
