# TERMS OF USE FOR COMMON GRAPHIC FRAMEWORKS AND ENGINES

A technical exploration and guide

## Content

# 常见的图形框架和引擎的使用条款

### 1. **OpenGL / OpenGL ES**

- **使用条件**：OpenGL 和 OpenGL ES 是由 Khronos Group 维护的开放标准，是免费的，任何开发者都可以在符合许可证的前提下使用。
- **是否付费**：完全免费，且不限制商业用途。

### 2. **Vulkan**

- **使用条件**：Vulkan 也是由 Khronos Group 维护的开放标准，与 OpenGL 类似，它是免费的，任何开发者都可以在符合许可证的前提下使用。
- **是否付费**：完全免费，并且不限制商业用途。

### 3. **DirectX**

- **使用条件**：DirectX 是微软的专有技术，通常用于 Windows 平台的开发。使用 DirectX 本身是免费的，开发者可以通过 Windows SDK 获得 DirectX 支持。
- **是否付费**：DirectX 本身免费，但是开发游戏发布在 Windows 平台上，可能需要获得 Microsoft Store 或其他平台的发布许可。

### 4. **Metal**

- **使用条件**：Metal 是苹果公司的专有图形 API，用于 iOS、macOS 和 tvOS 上。使用 Metal 本身是免费的，苹果提供了相关的开发工具和 API。
- **是否付费**：免费使用，但发布到 iOS 或 macOS 平台上，通常需要加入 Apple Developer Program，这需要每年支付 $99 美元的费用。

### 5. **SDL (Simple DirectMedia Layer)**

- **使用条件**：SDL 是一个开源的多媒体库，使用 zlib 许可证。任何开发者都可以免费使用 SDL，无论是个人项目还是商业项目。
- **是否付费**：完全免费，且适用于商业项目。

### 6. **Unreal Engine**

- **使用条件**：Unreal Engine 由 Epic Games 开发，并允许免费使用开发任何类型的项目。
- **是否付费**：Epic Games 对 Unreal Engine 的使用提供了免费和商业条款：
  - 免费使用 Unreal Engine 进行学习、原型开发和发布免费项目。
  - 一旦你的游戏或应用获得 $1,000,000 美元的收入，Epic Games 要求支付 5% 的版税（2020 年后调整为收入达到一定数额才需支付）。
  - 发布到 Epic Games Store 的项目免除版税。

### 7. **Unity**

- **使用条件**：Unity 提供不同的许可条款，包括免费和付费版本。开发者可以根据项目规模选择合适的许可证。
- **是否付费**：
  - **免费版本**：Unity Personal 是免费的，适用于年收入低于 $200,000 美元的个人或小团队。
  - **付费版本**：Unity Plus 和 Unity Pro 需要付费订阅，价格分别为 $399 和 $2,040 每年，适用于年收入较高的团队和公司。

### 8. **Cocos2d-x**

- **使用条件**：Cocos2d-x 是一个开源的跨平台游戏引擎，使用 MIT 许可证。它对个人和商业项目均免费，允许自由修改、分发和商业使用。
- **是否付费**：完全免费，无需支付任何费用。

### 9. **BGFX**

- **使用条件**：BGFX 是一个开源的跨平台图形渲染库，使用 BSD 许可证。可以自由使用 BGFX 开发个人和商业项目。
- **是否付费**：完全免费，适用于个人和商业用途。

### 10. **Raylib**

- **使用条件**：Raylib 是一个开源的图形库，使用 zlib 许可证，允许任何个人和商业项目使用。
- **是否付费**：完全免费，适合开发商用或个人项目。

### 总结

大多数图形 API（如 OpenGL、Vulkan、DirectX 和 Metal）都是免费的，任何人都可以使用，商业用途也无需支付额外费用。对于游戏引擎（如 Unreal 和 Unity），则有具体的付费条款，主要根据项目的商业规模和收入来决定是否需要支付版税或订阅费用。
