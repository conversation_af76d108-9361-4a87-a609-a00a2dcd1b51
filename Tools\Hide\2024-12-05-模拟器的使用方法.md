# 模拟器的使用方法

A technical exploration and guide

## Content



# 模拟器的使用方法

## 概述

在 Docker Hub 上有多种完整的路由器和交换机模拟器资源，尤其是支持容器化网络操作系统（NOS）的模拟环境。以下是几个常见的选项：

### 1. **vrnetlab**

- **vrnetlab** 是一个流行的开源网络模拟器，它使用 Docker 和 KVM 来运行虚拟路由器。它支持多种品牌的虚拟路由器，如 Cisco XRv、Arista vEOS、Juniper vMX 等。
- vrnetlab 允许你快速设置虚拟路由器网络，并通过简单的 Docker 命令管理这些设备，适合用于测试网络配置、开发和研究【32†source】。

### 2. **Containerlab**

- **Containerlab** 是一个专门用于容器化网络模拟的工具，它支持各种网络操作系统，如 Nokia SR Linux、Juniper vJunos、Cisco XRd、Arista cEOS、FRRouting（FRR）等。
- Containerlab 提供了便捷的命令行接口来部署和管理复杂的网络拓扑，支持容器化和虚拟机的混合网络，适合创建复杂的实验室环境【33†source】【34†source】。

### 3. **Wistar**

- **Wistar** 是 Juniper 开发的开源网络模拟工具，支持使用虚拟机和容器化的方式来模拟多种网络设备，包括 Linux 路由器和交换机。它主要用于创建基于虚拟机的网络拓扑，适合网络测试和验证【35†source】。

这些工具可以帮助你在 Docker 环境中模拟不同品牌的路由器和交换机，轻松创建和测试各种网络拓扑。具体的模拟器取决于你需要的品牌和设备功能，Containerlab 是一个非常灵活的选择，支持多种品牌和网络操作系统。

## vrnetlab

在 Windows 11 上安装和运行 **vrnetlab** 需要通过 Docker Desktop 环境来完成。以下是详细的步骤，帮助你在 Docker 环境中运行 vrnetlab：

### 步骤 1: 安装 Docker Desktop

1. 如果你还没有安装 Docker Desktop，请前往 [Docker 官网](https://www.docker.com/products/docker-desktop/) 下载并安装 Docker Desktop。
2. 安装完成后，启动 Docker Desktop 并确保它在运行。

### 步骤 2: 安装 vrnetlab

1. 打开 Windows 终端（如 PowerShell 或 CMD）。
2. 运行以下命令，从 GitHub 上克隆 vrnetlab 仓库：
   ```bash
   git clone https://github.com/plajjan/vrnetlab.git
   ```
3. 进入 `vrnetlab` 目录：
   ```bash
   cd vrnetlab
   ```

### 步骤 3: 构建 vrnetlab 镜像

根据你需要的路由器品牌（如 Cisco、Juniper、Arista），需要构建相应的镜像。以下以 Cisco XRv 为例：

1. 进入 Cisco XRv 目录：

   ```bash
   cd xrv
   ```
2. 使用以下命令构建 Docker 镜像：

   ```bash
   docker build -t vr-xrv .
   ```

   该过程会从基础镜像中构建 Cisco XRv 的 Docker 镜像，可能需要一定时间。

**如果这里出问题，可以考虑修改dockerfile, 将镜像从 debian:bullseye  改为 ubuntu:latest**

另外，这个dockerfile 安装的软件时间大概有 5分钟


### 步骤 4: 运行 vrnetlab 路由器

镜像构建完成后，你可以使用以下命令运行路由器：

```bash
docker run --rm --privileged --name vr-xrv vr-xrv
```

- `--rm` 表示在停止容器时自动删除它。
- `--privileged` 允许容器访问所有设备。
- `--name vr-xrv` 为该容器命名。

### 步骤 5: 访问路由器 CLI

当容器启动后，你可以通过以下方式访问路由器的 CLI：

1. 使用 Docker 的 `telnet` 命令连接到设备：

   ```bash
   telnet localhost 5000
   ```

   端口 `5000` 映射为设备的控制台，之后你可以像在物理设备上那样输入命令并进行配置。

### 步骤 6: 停止 vrnetlab

要停止 vrnetlab 路由器，只需运行：

```bash
docker stop vr-xrv
```

### 其他品牌的支持

如果你需要模拟其他品牌（如 Juniper、Arista 等），只需进入相应的 vrnetlab 子目录并重复上述步骤。具体支持的设备可以在 [vrnetlab 的 GitHub 页面](https://github.com/plajjan/vrnetlab) 中查看【32†source】【33†source】。
