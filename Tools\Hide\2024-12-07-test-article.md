# TEST ARTICLE

A technical exploration and guide

## Content

# 测试文章：使用Markdown写作指南

这是一篇测试文章，用来展示Markdown的基本语法和使用方法。

## 1. 标题使用

Markdown中的标题使用`#`符号表示，一级标题使用一个`#`，二级标题使用两个`#`，以此类推。

## 2. 文本格式化

- **粗体** 使用两个星号
- *斜体* 使用一个星号
- ~~删除线~~ 使用两个波浪线

## 3. 列表

### 无序列表
- 项目1
- 项目2
- 项目3

### 有序列表
1. 第一步
2. 第二步
3. 第三步

## 4. 代码展示

行内代码使用反引号：`console.log('Hello World')`

代码块使用三个反引号：

```javascript
function greeting(name) {
    return `Hello, ${name}!`;
}
```

## 5. 引用

> 这是一个引用示例
> 可以有多行

## 6. 链接和图片

[这是一个链接](https://example.com)

![图片描述](https://example.com/image.jpg)

## 7. 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 内容1 | 内容2 | 内容3 |
| 示例1 | 示例2 | 示例3 |

---

# Test Article

Welcome to my first blog post! This article demonstrates various Markdown features that are supported in this blog.

## Text Formatting

You can write text in **bold**, *italic*, or ***both***. You can also ~~strikethrough~~ text.

## Lists

Here's an unordered list:
- Item 1
- Item 2
- Item 3

And a numbered list:
1. First item
2. Second item
3. Third item

## Code Blocks

Here's some inline `code` and a code block:

```python
def hello_world():
    print("Hello, World!")
```

## Tables

| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |
| Cell 3   | Cell 4   |

## Task Lists

- [x] Create blog structure
- [x] Add test article
- [ ] Add more content

## Links

Visit [GitHub](https://github.com) for more information.

## Images

Here's how to include an image (replace with your own image URL):

![Sample Image](https://via.placeholder.com/600x400)

## Videos

And here's how to include a video (replace with your own video URL):

<video src="https://example.com/sample-video.mp4" controls></video>

This concludes our test article demonstrating various Markdown features!
