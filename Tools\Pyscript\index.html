<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Python Terminal in Browser</title>
    <!-- 引入 PyScript -->
    <link rel="stylesheet" href="https://pyscript.net/alpha/pyscript.css" />
    <script defer src="https://pyscript.net/alpha/pyscript.js"></script>
    <style>
      /* 自定义 py-repl 的大小和样式，模仿 PyScript 官方页面的终端风格 */
      py-repl {
        width: 80%; /* 设置宽度为页面的80% */
        height: 400px; /* 设置高度 */
        font-size: 14px; /* 字体大小略微调小 */
        background-color: #111; /* 更深的黑色背景 */
        color: #0e100e; /* 白色字体 */
        font-family: "Courier New", Courier, monospace; /* 等宽字体 */
        overflow-y: auto; /* 启用纵向滚动条 */
      }
      body {
        color: #00ff00; /* 白色字体 */
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        flex-direction: column; /* 垂直方向布局 */
      }
      h1 {
        color: #ffa500; /* 设置标题为亮橙色，突出效果 */
        font-family: "Courier New", Courier, monospace; /* 等宽字体 */
      }
      /* 模拟终端提示符的样式 */
      .repl-prompt {
        color: #00ff00; /* 绿色提示符 */
      }
      /* 调整整个页面的最大宽度，避免过度拉伸 */
      .terminal-container {
        max-width: 1000px;
      }
    </style>
  </head>
  <body>
    <div class="terminal-container">
      <h1>Python Terminal in Browser</h1>

      <!-- 使用 py-repl 提供终端输入，设置自定义大小和样式 -->
      <py-repl></py-repl>
    </div>
  </body>
</html>
