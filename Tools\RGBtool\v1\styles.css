/* Reset some default styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f0f0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

h1 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 20px;
}

.input-container {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.input-container label {
    margin-right: 10px;
    font-size: 1.2rem;
    color: #333;
}

.input-container input {
    padding: 10px;
    font-size: 1rem;
    width: 60px;
    border: 2px solid #ccc;
    border-radius: 5px;
}

button {
    padding: 10px 20px;
    font-size: 1rem;
    color: #fff;
    background-color: #007bff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-top: 10px;
}

button:hover {
    background-color: #0056b3;
}

#hexColor {
    padding: 10px;
    font-size: 1rem;
    width: 120px;
    border: 2px solid #ccc;
    border-radius: 5px;
    margin-top: 10px;
}

#colorDisplay {
    width: 200px;
    height: 200px;
    background-color: #000000;
    margin-top: 20px;
    border: 3px solid #ccc;
    border-radius: 10px;
}

table {
    width: 100%;
    max-width: 800px;
    margin-top: 20px;
    border-collapse: collapse;
    border: 1px solid #ccc;
}

table th, table td {
    padding: 10px;
    text-align: center;
    border: 1px solid #ccc;
}

table th {
    background-color: #f2f2f2;
}

tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}
