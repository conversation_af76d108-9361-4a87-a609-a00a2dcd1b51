body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f4f4f4;
}

#app {
    background-color: #fff;
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1, h2 {
    text-align: center;
}

label, input, select, button {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

input, select {
    padding: 10px;
    font-size: 1rem;
}

button {
    background-color: #007BFF;
    color: white;
    padding: 10px;
    font-size: 1.2rem;
    cursor: pointer;
    border: none;
}

button:hover {
    background-color: #0056b3;
}

#output {
    margin-top: 20px;
    padding: 20px;
    background-color: #e9ecef;
    border-radius: 5px;
}

#output p {
    font-size: 1.1rem;
}

#formula {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-size: 1rem;
    white-space: pre-wrap;
}
