/* 全局样式 */
body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

#app {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 30px;
  max-width: 600px;
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
}

h1,
h2 {
  text-align: center;
  color: #333;
}

label {
  font-weight: bold;
  color: #444;
  margin-bottom: 5px;
  display: block;
}

input,
select {
  padding: 12px;
  font-size: 1rem;
  width: calc(100% - 24px);
  margin-bottom: 20px;
  border: 2px solid #ccc;
  border-radius: 8px;
  outline: none;
  transition: border-color 0.3s ease;
}

input:focus,
select:focus {
  border-color: #007bff;
}

button {
  background-color: #007bff;
  color: white;
  padding: 12px;
  font-size: 1.2rem;
  cursor: pointer;
  border: none;
  border-radius: 8px;
  width: 100%;
  transition: background-color 0.3s ease;
}

button:hover {
  background-color: #0056b3;
}

#output {
  margin-top: 20px;
  padding: 20px;
  background-color: #f1f1f1;
  border-radius: 10px;
  border: 1px solid #ddd;
}

#output p {
  font-size: 1.2rem;
  color: #555;
}

#formula {
  background-color: #eef;
  padding: 12px;
  border-radius: 8px;
  font-size: 1rem;
  white-space: pre-wrap;
  color: #333;
  margin-top: 10px;
}

#preTaxPrice,
#vatAmount,
#postTaxPrice {
  color: #007bff;
  font-weight: bold;
}
