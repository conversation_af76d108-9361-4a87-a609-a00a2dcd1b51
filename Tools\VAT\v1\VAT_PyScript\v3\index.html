<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>增值税计算器</title>
    <link rel="stylesheet" href="style.css" />
    <!-- 引入 PyScript -->
    <link rel="stylesheet" href="https://pyscript.net/alpha/pyscript.css" />
    <script defer src="https://pyscript.net/alpha/pyscript.js"></script>
  </head>
  <body>
    <label for="rate">输入税率（%）:</label>
    <input
      type="number"
      id="rate"
      placeholder="请输入税率"
      step="0.01"
      min="0"
      pys-onInput="on_rate_input"
    />

    <label for="price">输入商品价格：</label>
    <input
      type="number"
      id="price"
      placeholder="商品价格"
      step="0.01"
      min="0"
    />

    <button id="calculateBtn" pys-onClick="calculate_vat">计算增值税</button>

    <div id="output">
      <h2>计算结果：</h2>
      <p>
        <strong>价外税（不含税价格）：</strong>
        <span id="preTaxPrice">-</span>
      </p>
      <p><strong>增值税金额：</strong> <span id="vatAmount">-</span></p>
      <p>
        <strong>价内税（含税价格）：</strong> <span id="postTaxPrice">-</span>
      </p>
      <h3>计算公式：</h3>
      <p id="formula">-</p>
    </div>

    <!-- 引用独立的 Python 文件 -->
    <py-script src="exec.py"></py-script>
  </body>
</html>
