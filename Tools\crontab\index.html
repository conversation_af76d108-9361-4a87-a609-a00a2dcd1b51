<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cron Expression Generator with Bilingual Explanation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f7f6;
            margin: 20px;
            color: #333;
        }
        .container {
            display: flex;
            max-width: 1200px;
            margin: 0 auto;
        }
        .left, .right {
            flex: 1;
            padding: 20px;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        .left {
            margin-right: 20px;
        }
        h1 {
            text-align: center;
            color: #4CAF50;
            font-size: 1.5em;
        }
        label {
            font-weight: bold;
            font-size: 0.9em;
        }
        input, button {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 0.9em;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 0.9em;
        }
        button:hover {
            background-color: #45a049;
        }
        .output {
            margin-top: 10px;
            font-size: 0.9em;
        }
        .output p {
            background: #f1f1f1;
            padding: 10px;
            border-radius: 5px;
        }
        .output h3 {
            color: #333;
            font-size: 1em;
        }
        .right h3 {
            color: #4CAF50;
            margin-bottom: 15px;
            font-size: 1em;
        }
        .right pre {
            background-color: #f4f7f6;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 0.85em;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Left section with form and cron explanation -->
        <div class="left">
            <h1>Cron Expression Generator</h1>
            <form id="cronForm">
                <div class="output">
                    <h3>Cron Expression (表达式):</h3>
                    <p id="output"></p>

                    <h3>Explanation (解释):</h3>
                    <p id="explanation_en"></p>
                    <p id="explanation_zh"></p>
                </div>

                <label>Minute (分钟): </label>
                <input type="text" id="minute" placeholder="0-59"><br>
                <label>Hour (小时): </label>
                <input type="text" id="hour" placeholder="0-23"><br>
                <label>Day of Month (日期): </label>
                <input type="text" id="day" placeholder="1-31"><br>
                <label>Month (月份): </label>
                <input type="text" id="month" placeholder="1-12"><br>
                <label>Day of Week (星期几): </label>
                <input type="text" id="weekday" placeholder="0-7 (Sun=0)"><br>
                <button type="button" onclick="location.reload();">Reset (复位)</button>
            </form>
        </div>

        <!-- Right section with common cron tasks examples -->
        <div class="right">
            <h3>Common Cron Commands (常见命令):</h3>
            <pre>
# 备份文件夹到指定位置:
tar -czf /backup/home.tar.gz /home/<USER>

# 清理临时文件夹:
rm -rf /tmp/*

# 同步系统时间:
ntpdate time.nist.gov

# 数据库备份 (MySQL):
mysqldump -u root -p password database_name > /backup/db_backup.sql

# 更新系统软件包 (Debian/Ubuntu):
apt-get update && apt-get upgrade -y

# 重启 Web 服务器 (Apache):
systemctl restart apache2

# 同步文件到远程服务器 (rsync):
rsync -avz /local/dir/ user@remote:/remote/dir/

# 监控磁盘空间并发送邮件:
df -h | mail -s "Disk Usage Report" <EMAIL>

# 删除超过 30 天的日志文件:
find /var/log -type f -mtime +30 -exec rm -f {} \;

# 定期重启系统:
reboot
            </pre>
        </div>
    </div>

    <script>
        // 解析并生成cron表达式的中英文解释
        function parseCron(minute, hour, day, month, weekday) {
            let minuteExp = (minute === '*') ? 'every minute' : `at minute ${minute}`;
            let hourExp = (hour === '*') ? 'every hour' : `at hour ${hour}`;
            let dayExp = (day === '*') ? 'every day' : `on day ${day}`;
            let monthExp = (month === '*') ? 'every month' : `in month ${month}`;
            let weekdayExp = (weekday === '*') ? 'every day of the week' : `on weekday ${weekday}`;
            
            let minuteZh = (minute === '*') ? '每分钟' : `在第 ${minute} 分钟`;
            let hourZh = (hour === '*') ? '每小时' : `在第 ${hour} 小时`;
            let dayZh = (day === '*') ? '每天' : `每月第 ${day} 天`;
            let monthZh = (month === '*') ? '每月' : `在 ${month} 月`;
            let weekdayZh = (weekday === '*') ? '每周的每天' : `在星期 ${weekday}`;

            return {
                en: `${minuteExp}, ${hourExp}, ${dayExp}, ${monthExp}, ${weekdayExp}`,
                zh: `${minuteZh}, ${hourZh}, ${dayZh}, ${monthZh}, ${weekdayZh}`
            };
        }

        function updateCronDescription() {
            const minute = document.getElementById('minute').value || '*';
            const hour = document.getElementById('hour').value || '*';
            const day = document.getElementById('day').value || '*';
            const month = document.getElementById('month').value || '*';
            const weekday = document.getElementById('weekday').value || '*';
            
            const cronExpression = `${minute} ${hour} ${day} ${month} ${weekday}`;
            const explanation = parseCron(minute, hour, day, month, weekday);
            
            document.getElementById('output').innerText = cronExpression;
            document.getElementById('explanation_en').innerText = explanation.en;
            document.getElementById('explanation_zh').innerText = explanation.zh;
        }

        // 绑定输入框事件，实时更新描述
        document.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', updateCronDescription);
        });

        // 初始化时显示默认值的描述
        updateCronDescription();
    </script>
</body>
</html>
