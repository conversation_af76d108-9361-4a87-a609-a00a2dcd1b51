<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>在线五子棋</title>
  <style>
    #board {
      border: 1px solid black;
      background-color: #8fbc8f;
      display: block;
      margin: 20px auto;
    }
  </style>
  <style>
    #controls {
      display: flex;
      flex-direction: column;
      position: absolute;
      top: 20px;
      right: 50px;
    }
    #controls button {
      margin-bottom: 10px;
      padding: 10px;
    }
  </style>
  <link href="https://fonts.googleapis.com/css2?family=ZCOOL+QingKe+HuangYou&display=swap" rel="stylesheet">
</head>
<body>
  
  <div id="controls">
    <button id="playerVsPlayer">玩家 vs 玩家</button>
    <button id="playerVsComputer">玩家 vs 电脑</button>
    <button id="newGame">新游戏</button>
    <button id="undo">上一步</button>
  </div>
  <h2 style="text-align: center;">在线五子棋</h2>
  <canvas id="board" width="600" height="600"></canvas>
  <div id="result" style="text-align: center; color: red; font-weight: bold; margin-top: 10px; font-size: 2em; font-family: 'ZCOOL QingKe HuangYou', cursive;"></div>
  <script>
    const canvas = document.getElementById('board');
    const playerVsPlayerButton = document.getElementById('playerVsPlayer');
    const playerVsComputerButton = document.getElementById('playerVsComputer');
    let isPlayerVsComputer = false;
    let gameStarted = false;
    const newGameButton = document.getElementById('newGame');
    const undoButton = document.getElementById('undo');
    let moves = [];
    const ctx = canvas.getContext('2d');
    const boardSize = 15;
    const cellSize = canvas.width / boardSize;
    let board = Array.from({ length: boardSize }, () => Array(boardSize).fill(null));
    let currentPlayer = 'black';
    let playerColor = 'black';
    let blackPieceColor = 'black';
    let whitePieceColor = 'white';

    // 绘制棋盘
    function drawBoard() {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.strokeStyle = '#000';
      for (let i = 0; i < boardSize; i++) {
        ctx.beginPath();
        ctx.moveTo(i * cellSize, 0);
        ctx.lineTo(i * cellSize, canvas.height);
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(0, i * cellSize);
        ctx.lineTo(canvas.width, i * cellSize);
        ctx.stroke();
      }
    }

    // 绘制棋子
    function drawPiece(x, y, color) {
      ctx.beginPath();
      ctx.arc((x + 0.5) * cellSize, (y + 0.5) * cellSize, cellSize / 2.5, 0, 2 * Math.PI);
      ctx.fillStyle = color;
      ctx.fill();
      ctx.closePath();
    }

    // 棋子胜利检测
    function checkWin(x, y, color) {
      function countDirection(dx, dy) {
        let count = 0;
        let nx = x + dx;
        let ny = y + dy;
        while (nx >= 0 && nx < boardSize && ny >= 0 && ny < boardSize && board[nx][ny] === color) {
          count++;
          nx += dx;
          ny += dy;
        }
        return count;
      }

      const directions = [
        [1, 0], [0, 1], [1, 1], [1, -1]
      ];

      for (const [dx, dy] of directions) {
        const count = 1 + countDirection(dx, dy) + countDirection(-dx, -dy);
        if (count >= 5) {
          return true;
        }
      }
      return false;
    }

    // 处理玩家点击事件
    function handleClick(e) {
      if (!gameStarted) return;
      const rect = canvas.getBoundingClientRect();
      const x = Math.floor((e.clientX - rect.left) / cellSize);
      const y = Math.floor((e.clientY - rect.top) / cellSize);

      if (board[x][y] === null) {
        board[x][y] = currentPlayer;
        moves.push({ x, y, color: currentPlayer });
        drawPiece(x, y, currentPlayer === 'black' ? blackPieceColor : whitePieceColor);
        if (checkWin(x, y, currentPlayer)) {
          document.getElementById('result').innerText = `${currentPlayer === 'black' ? '黑方' : '白方'} 获胜！`;
          canvas.removeEventListener('click', handleClick);
        } else {
          currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
          if (isPlayerVsComputer && currentPlayer !== playerColor) {
            setTimeout(() => {
              computerMove();
            }, 500);
          }
        }
      }
    }

    newGameButton.addEventListener('click', () => {
      startNewGame(playerColor);
    });

    function startNewGame(playerColorSelection = 'black') {
      playerColor = playerColorSelection;
      currentPlayer = 'black';
      document.getElementById('result').innerText = '';
      canvas.addEventListener('click', handleClick);
      board = Array.from({ length: boardSize }, () => Array(boardSize).fill(null));
      moves = [];
      gameStarted = true;
      drawBoard();

      if (isPlayerVsComputer && playerColor === 'white') {
        setTimeout(() => {
          computerMove();
        }, 500);
      }
    }
    drawBoard();

    playerVsPlayerButton.addEventListener('click', () => {
      isPlayerVsComputer = false;
      startNewGame();
    });

    playerVsComputerButton.addEventListener('click', () => {
      const selectionModal = document.createElement('div');
      selectionModal.style.position = 'fixed';
      selectionModal.style.top = '50%';
      selectionModal.style.left = '50%';
      selectionModal.style.transform = 'translate(-50%, -50%)';
      selectionModal.style.backgroundColor = '#fff';
      selectionModal.style.padding = '20px';
      selectionModal.style.boxShadow = '0px 0px 10px rgba(0,0,0,0.5)';
      selectionModal.innerHTML = `
        <div style="text-align: center; margin-bottom: 10px;">请选择：</div>
        <button id="chooseBlack" style="margin: 5px;">执黑</button>
        <button id="chooseWhite" style="margin: 5px;">执白</button>
        <button id="cancelSelection" style="margin: 5px;">取消</button>
      `;
      document.body.appendChild(selectionModal);

      document.getElementById('chooseBlack').addEventListener('click', () => {
        isPlayerVsComputer = true;
        startNewGame('black');
        document.body.removeChild(selectionModal);
      });

      document.getElementById('chooseWhite').addEventListener('click', () => {
        isPlayerVsComputer = true;
        startNewGame('white');
        document.body.removeChild(selectionModal);
      });

      document.getElementById('cancelSelection').addEventListener('click', () => {
        document.body.removeChild(selectionModal);
      });
    });

    undoButton.addEventListener('click', () => {
      if (moves.length > 0) {
        const lastMove = moves.pop();
        board[lastMove.x][lastMove.y] = null;
        drawBoard();
        moves.forEach(move => drawPiece(move.x, move.y, move.color));
        currentPlayer = lastMove.color === 'black' ? 'white' : 'black';
      }
    });

    canvas.addEventListener('click', handleClick);

    drawBoard();

    function computerMove() {
      let emptyCells = [];
      for (let i = 0; i < boardSize; i++) {
        for (let j = 0; j < boardSize; j++) {
          if (board[i][j] === null) {
            emptyCells.push({ x: i, y: j });
          }
        }
      }
      if (emptyCells.length > 0) {
        let move = emptyCells[Math.floor(Math.random() * emptyCells.length)];
        board[move.x][move.y] = currentPlayer;
        moves.push({ x: move.x, y: move.y, color: currentPlayer });
        drawPiece(move.x, move.y, currentPlayer === 'black' ? blackPieceColor : whitePieceColor);
        if (checkWin(move.x, move.y, currentPlayer)) {
          document.getElementById('result').innerText = `${currentPlayer === 'black' ? '黑方' : '白方'} 获胜！`;
          canvas.removeEventListener('click', handleClick);
        } else {
          currentPlayer = currentPlayer === 'black' ? 'white' : 'black';
        }
      }
    }
  </script>
</body>
</html>