<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字转换工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            font-size: 16px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
            border-radius: 8px;
            position: relative;
        }
        .result h3 {
            font-size: 16px;
            margin-bottom: 5px;
        }
        .result span {
            font-size: 14px;
            display: inline-block;
            word-wrap: break-word;
            width: calc(100% - 80px);
        }
        .copy-btn {
            position: absolute;
            right: 10px;
            top: 20px;
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .copy-btn:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>阿拉伯数字转换为中文大写数字</h1>
    <label for="arabicInput">输入数字（自动添加千分位）:</label>
    <input type="text" id="arabicInput" placeholder="输入金额..." />

    <div class="result">
        <h3>普通数字: <span id="normalNumber">-</span></h3>
        <button class="copy-btn" onclick="copyToClipboard('normalNumber')">复制</button>
    </div>

    <div class="result">
        <h3>大写数字: <span id="capitalNumber">-</span></h3>
        <button class="copy-btn" onclick="copyToClipboard('capitalNumber')">复制</button>
    </div>
</div>

<script src="script.js"></script>
</body>
</html>
