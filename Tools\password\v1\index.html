<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位转换</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 页面整体布局 */
        body {
            display: flex;
            margin: 0;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        /* 左侧导航栏样式 */
        #nav {
            width: 250px;
            background-color: #f4f4f4;
            border-right: 1px solid #ccc;
            padding: 20px;
            overflow-y: auto;
        }

        /* 右侧内容区域样式 */
        #content {
            flex-grow: 1;
            padding: 20px;
            background-color: #fff;
        }

        /* 工具列表项样式 */
        .tool-item {
            margin-bottom: 15px;
        }

        .tool-item a {
            display: block;
            padding: 10px;
            text-decoration: none;
            color: #333;
            background-color: #e6e6e6;
            border-radius: 5px;
        }

        .tool-item a:hover {
            background-color: #ccc;
        }

        /* iframe 样式 */
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 左侧导航栏 -->
    <div id="nav">
        <h2>随机密码</h2>
        <div id="tools-list"></div>
    </div>

    <!-- 右侧内容区 -->
    <div id="content">
        <iframe id="tool-display" src="" title="随机密码"></iframe>
    </div>

    <script>
        // 加载工具列表并显示在左侧导航栏
        fetch('tools.json')
            .then(response => response.json())
            .then(data => {
                const toolsList = document.getElementById('tools-list');
                const toolDisplay = document.getElementById('tool-display');

                data.forEach(tool => {
                    const toolItem = document.createElement('div');
                    toolItem.classList.add('tool-item');

                    const toolLink = document.createElement('a');
                    toolLink.href = "#";
                    toolLink.textContent = tool.name;
                    toolLink.addEventListener('click', () => {
                        toolDisplay.src = tool.url; // 在右侧iframe中加载工具页面
                    });

                    const toolDesc = document.createElement('p');
                    toolDesc.textContent = tool.description;

                    toolItem.appendChild(toolLink);
                    toolsList.appendChild(toolItem);
                });
            })
            .catch(error => {
                console.error('加载工具列表失败:', error);
            });
    </script>
</body>
</html>
