<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机密码生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        #result {
            font-weight: bold;
            color: green;
        }
        button {
            margin-top: 10px;
            cursor: pointer;
        }
        p.notice {
            color: red;
            font-size: 14px;
        }
    </style>
    <script>
        // 生成随机密码函数
        function generatePassword() {
            const length = document.getElementById('passwordLength').value;
            const complexity = document.getElementById('complexity').value;
            const characters = {
                low: 'abcdefghijklmnopqrstuvwxyz',
                medium: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
                high: 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+[]{}|;:,.<>?/`~'
            };
            
            let charSet = characters[complexity];
            let password = '';

            for (let i = 0; i < length; i++) {
                const randomIndex = Math.floor(Math.random() * charSet.length);
                password += charSet[randomIndex];
            }

            document.getElementById('result').textContent = password;
            document.getElementById('passwordContainer').style.display = 'block';
            document.getElementById('notice').style.display = 'block';
        }

        // 复制密码到剪切板
        function copyToClipboard() {
            const password = document.getElementById('result').textContent;
            navigator.clipboard.writeText(password).then(() => {
                alert('密码已复制到剪切板！');
            }).catch(err => {
                alert('复制失败，请手动复制。');
            });
        }
    </script>
</head>
<body>
    <h1>随机密码生成器</h1>
    <label for="passwordLength">选择密码长度：</label>
    <input type="number" id="passwordLength" value="12" min="8" max="64">
    
    <label for="complexity">选择安全策略：</label>
    <select id="complexity">
        <option value="low">低（仅小写字母）</option>
        <option value="medium">中（大小写字母+数字）</option>
        <option value="high">高（大小写字母+数字+特殊符号）</option>
    </select>

    <button onclick="generatePassword()">生成密码</button>
    
    <div id="passwordContainer" style="display:none;">
        <p>生成的密码：<span id="result"></span></p>
        <button onclick="copyToClipboard()">复制密码</button>
    </div>
    
    <p class="notice" id="notice" style="display:none;">注意：请妥善保存您的密码，避免丢失或泄露。</p>
</body>
</html>
