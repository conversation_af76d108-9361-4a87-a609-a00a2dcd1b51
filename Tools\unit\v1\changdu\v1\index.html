<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>长度单位转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        label, select, input {
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .result {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>长度单位转换器</h1>
        <h3>公里(km)、米(m)、分米(dm)、厘米(cm)、里、丈、尺、寸、分、厘、海里(nmi)、英寻、英里、弗隆(fur)、码(yd)、英尺(ft)、英寸(in)、毫米(mm)、微米(um)</h3>
        <label for="inputValue">输入长度</label>
        <input type="number" id="inputValue" placeholder="输入数值">

        <label for="unitSelect">选择单位</label>
        <select id="unitSelect">
            <option value="km">公里 (km)</option>
            <option value="m">米 (m)</option>
            <option value="dm">分米 (dm)</option>
            <option value="cm">厘米 (cm)</option>
            <option value="li">里</option>
            <option value="zhang">丈</option>
            <option value="chi">尺</option>
            <option value="cun">寸</option>
            <option value="fen">分</option>
            <option value="li2">厘</option>
            <option value="nmi">海里 (nmi)</option>
            <option value="fathom">英寻</option>
            <option value="mile">英里</option>
            <option value="fur">弗隆 (fur)</option>
            <option value="yd">码 (yd)</option>
            <option value="ft">英尺 (ft)</option>
            <option value="in">英寸 (in)</option>
            <option value="mm">毫米 (mm)</option>
            <option value="um">微米 (um)</option>
        </select>

        <button onclick="convert()">转换</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        const conversionRates = {
            km: 1000, // 1 公里 = 1000 米
            m: 1, // 1 米 = 1 米
            dm: 0.1, // 1 分米 = 0.1 米
            cm: 0.01, // 1 厘米 = 0.01 米
            li: 500, // 1 里 = 500 米
            zhang: 3.3333, // 1 丈 = 3.3333 米
            chi: 0.3333, // 1 尺 = 0.3333 米
            cun: 0.03333, // 1 寸 = 0.03333 米
            fen: 0.003333, // 1 分 = 0.003333 米
            li2: 0.0003333, // 1 厘 = 0.0003333 米
            nmi: 1852, // 1 海里 = 1852 米
            fathom: 1.8288, // 1 英寻 = 1.8288 米
            mile: 1609.34, // 1 英里 = 1609.34 米
            fur: 201.168, // 1 弗隆 = 201.168 米
            yd: 0.9144, // 1 码 = 0.9144 米
            ft: 0.3048, // 1 英尺 = 0.3048 米
            in: 0.0254, // 1 英寸 = 0.0254 米
            mm: 0.001, // 1 毫米 = 0.001 米
            um: 0.000001 // 1 微米 = 0.000001 米
        };

        function convert() {
            const inputValue = parseFloat(document.getElementById('inputValue').value);
            const selectedUnit = document.getElementById('unitSelect').value;

            if (isNaN(inputValue)) {
                document.getElementById('result').innerText = '请输入有效的数值。';
                return;
            }

            const resultElement = document.getElementById('result');
            resultElement.innerHTML = ''; // 清空之前的结果

            const baseValue = inputValue * conversionRates[selectedUnit]; // 将输入值转换为米

            for (const unit in conversionRates) {
                if (unit !== selectedUnit) { // 只显示与输入单位不同的转换结果
                    const convertedValue = baseValue / conversionRates[unit];
                    const unitName = document.querySelector(`option[value=${unit}]`).textContent;
                    resultElement.innerHTML += `<p>${convertedValue.toFixed(6)} ${unitName}</p>`;
                }
            }
        }
    </script>
</body>
</html>
