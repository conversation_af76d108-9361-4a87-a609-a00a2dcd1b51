<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进制转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        label, select, input {
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .result {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>进制转换器</h1>
        <label for="inputValue">输入数值</label>
        <input type="text" id="inputValue" placeholder="输入数值">

        <label for="fromBase">从进制</label>
        <select id="fromBase">
            <!-- 动态生成进制选项 -->
            <script>
                for (let i = 2; i <= 64; i++) {
                    document.write(`<option value="${i}">${i} 进制</option>`);
                }
            </script>
        </select>

        <label for="toBase">转换为进制</label>
        <select id="toBase">
            <!-- 动态生成进制选项 -->
            <script>
                for (let i = 2; i <= 64; i++) {
                    document.write(`<option value="${i}">${i} 进制</option>`);
                }
            </script>
        </select>

        <button onclick="convert()">转换</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        const charSet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/'; // 64 个字符

        function toDecimal(value, base) {
            return [...value].reverse().reduce((acc, digit, index) => {
                const digitValue = charSet.indexOf(digit);
                if (digitValue >= base || digitValue === -1) {
                    throw new Error(`非法字符 ${digit} 对于 ${base} 进制`);
                }
                return acc + digitValue * Math.pow(base, index);
            }, 0);
        }

        function fromDecimal(value, base) {
            let result = '';
            while (value > 0) {
                result = charSet[value % base] + result;
                value = Math.floor(value / base);
            }
            return result || '0';
        }

        function convert() {
            const inputValue = document.getElementById('inputValue').value.trim();
            const fromBase = parseInt(document.getElementById('fromBase').value);
            const toBase = parseInt(document.getElementById('toBase').value);

            if (!inputValue) {
                document.getElementById('result').innerText = '请输入有效的数值。';
                return;
            }

            try {
                const decimalValue = toDecimal(inputValue, fromBase);
                const convertedValue = fromDecimal(decimalValue, toBase);
                document.getElementById('result').innerText = `${inputValue} 的 ${fromBase} 进制转换为 ${toBase} 进制: ${convertedValue}`;
            } catch (error) {
                document.getElementById('result').innerText = `错误: ${error.message}`;
            }
        }
    </script>
</body>
</html>
