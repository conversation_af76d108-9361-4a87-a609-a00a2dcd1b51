<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>体积单位转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        label, select, input {
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .result {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>体积单位转换器</h1>
        <label for="inputValue">输入体积</label>
        <input type="number" id="inputValue" placeholder="输入数值">

        <label for="unitSelect">选择单位</label>
        <select id="unitSelect">
            <option value="m3">立方米 (Cubic meter)</option>
            <option value="hl">公石 (hectoliter)</option>
            <option value="dal">十升 (dekaliter)</option>
            <option value="dm3">立方分米 (Cubic dm)</option>
            <option value="l">升 (liter)</option>
            <option value="dl">分升 (deciliter)</option>
            <option value="cl">厘升 (centiliter)</option>
            <option value="cm3">立方厘米 (Cubic cm)</option>
            <option value="ml">毫升 (milliliter)</option>
            <option value="mm3">立方毫米 (Cubic millimeter)</option>
            <option value="barrel">桶 (Barrel)</option>
            <option value="bushel">蒲式耳 (Bushel)</option>
            <option value="peck">配克 (Peck)</option>
            <option value="quart">夸脱 (Quart)</option>
            <option value="pint">品脱 (Pint)</option>
            <option value="gallon">加仑 (Gallon)</option>
            <option value="ounce">盎司 (Ounce)</option>
            <option value="dram">打兰 (Dram)</option>
            <option value="minim">量滴 (Minim)</option>
            <option value="yd3">立方码 (Cubic yard)</option>
            <option value="ft3">立方英尺 (Cubic foot)</option>
            <option value="in3">立方英寸 (Cubic inch)</option>
        </select>

        <button onclick="convert()">转换</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        const conversionRates = {
            m3: 1, // 1 立方米 = 1 立方米
            hl: 0.1, // 1 公石 = 0.1 立方米
            dal: 0.01, // 1 十升 = 0.01 立方米
            dm3: 0.001, // 1 立方分米 = 0.001 立方米
            l: 0.001, // 1 升 = 0.001 立方米
            dl: 0.0001, // 1 分升 = 0.0001 立方米
            cl: 0.00001, // 1 厘升 = 0.00001 立方米
            cm3: 0.000001, // 1 立方厘米 = 0.000001 立方米
            ml: 0.000001, // 1 毫升 = 0.000001 立方米
            mm3: 1e-9, // 1 立方毫米 = 1e-9 立方米
            barrel: 0.158987, // 1 桶 = 0.158987 立方米
            bushel: 0.0352391, // 1 蒲式耳 = 0.0352391 立方米
            peck: 0.00880977, // 1 配克 = 0.00880977 立方米
            quart: 0.00113652, // 1 夸脱 = 0.00113652 立方米
            pint: 0.000568261, // 1 品脱 = 0.000568261 立方米
            gallon: 0.00454609, // 1 加仑 = 0.00454609 立方米
            ounce: 0.0000295735, // 1 盎司 = 0.0000295735 立方米
            dram: 0.0000036967, // 1 打兰 = 0.0000036967 立方米
            minim: 0.00000006161, // 1 量滴 = 0.00000006161 立方米
            yd3: 0.764555, // 1 立方码 = 0.764555 立方米
            ft3: 0.0283168, // 1 立方英尺 = 0.0283168 立方米
            in3: 0.0000163871 // 1 立方英寸 = 0.0000163871 立方米
        };

        function convert() {
            const inputValue = parseFloat(document.getElementById('inputValue').value);
            const selectedUnit = document.getElementById('unitSelect').value;

            if (isNaN(inputValue)) {
                document.getElementById('result').innerText = '请输入有效的数值。';
                return;
            }

            const resultElement = document.getElementById('result');
            resultElement.innerHTML = ''; // 清空之前的结果

            const baseValue = inputValue * conversionRates[selectedUnit]; // 将输入值转换为立方米

            for (const unit in conversionRates) {
                if (unit !== selectedUnit) { // 只显示与输入单位不同的转换结果
                    const convertedValue = baseValue / conversionRates[unit];
                    const unitName = document.querySelector(`option[value=${unit}]`).textContent;
                    resultElement.innerHTML += `<p>${convertedValue.toFixed(6)} ${unitName}</p>`;
                }
            }
        }
    </script>
</body>
</html>
