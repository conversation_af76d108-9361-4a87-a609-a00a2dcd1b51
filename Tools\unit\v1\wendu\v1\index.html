<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>温度单位转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        label, select, input {
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .result {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>温度单位转换器</h1>
        <label for="inputValue">输入温度</label>
        <input type="number" id="inputValue" placeholder="输入温度">

        <label for="unitSelect">选择单位</label>
        <select id="unitSelect">
            <option value="celsius">摄氏度 (Celsius)</option>
            <option value="fahrenheit">华氏度 (Fahrenheit)</option>
            <option value="kelvin">开氏度 (Ke<PERSON>)</option>
            <option value="rankine">兰氏度 (Rankine)</option>
            <option value="reaumur">列氏度 (Reaumur)</option>
        </select>

        <button onclick="convert()">转换</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        function toCelsius(value, unit) {
            switch (unit) {
                case 'celsius': return value;
                case 'fahrenheit': return (value - 32) * 5 / 9;
                case 'kelvin': return value - 273.15;
                case 'rankine': return (value - 491.67) * 5 / 9;
                case 'reaumur': return value * 5 / 4;
            }
        }

        function fromCelsius(value, unit) {
            switch (unit) {
                case 'celsius': return value;
                case 'fahrenheit': return (value * 9 / 5) + 32;
                case 'kelvin': return value + 273.15;
                case 'rankine': return (value + 273.15) * 9 / 5;
                case 'reaumur': return value * 4 / 5;
            }
        }

        function convert() {
            const inputValue = parseFloat(document.getElementById('inputValue').value);
            const selectedUnit = document.getElementById('unitSelect').value;

            if (isNaN(inputValue)) {
                document.getElementById('result').innerText = '请输入有效的温度。';
                return;
            }

            const celsiusValue = toCelsius(inputValue, selectedUnit);

            const resultElement = document.getElementById('result');
            resultElement.innerHTML = ''; // 清空之前的结果

            const units = ['celsius', 'fahrenheit', 'kelvin', 'rankine', 'reaumur'];
            const unitNames = {
                'celsius': '摄氏度 (Celsius)',
                'fahrenheit': '华氏度 (Fahrenheit)',
                'kelvin': '开氏度 (Kelvin)',
                'rankine': '兰氏度 (Rankine)',
                'reaumur': '列氏度 (Reaumur)'
            };

            units.forEach(unit => {
                if (unit !== selectedUnit) {
                    const convertedValue = fromCelsius(celsiusValue, unit);
                    resultElement.innerHTML += `<p>${convertedValue.toFixed(2)} ${unitNames[unit]}</p>`;
                }
            });
        }
    </script>
</body>
</html>
