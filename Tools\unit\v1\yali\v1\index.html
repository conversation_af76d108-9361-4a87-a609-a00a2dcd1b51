<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>压力单位转换器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        label, select, input {
            margin: 10px 0;
            display: block;
            width: 100%;
        }
        .result {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>压力单位转换器</h1>
        <label for="inputValue">输入压力值</label>
        <input type="number" id="inputValue" placeholder="输入数值">

        <label for="unitSelect">选择单位</label>
        <select id="unitSelect">
            <option value="bar">巴 (Bar)</option>
            <option value="kpa">千帕 (kPa)</option>
            <option value="hpa">百帕 (hPa)</option>
            <option value="mbar">毫巴 (mbar)</option>
            <option value="pa">帕斯卡 (Pa)</option>
            <option value="atm">标准大气压 (atm)</option>
            <option value="mmHg">毫米汞柱 (mmHg)</option>
            <option value="psi">磅力每平方英寸 (psi)</option>
            <option value="mmH2O">毫米水柱 (mmH2O)</option>
            <option value="kgfcm2">公斤力每平方厘米 (kgf/cm²)</option>
        </select>

        <button onclick="convert()">转换</button>

        <div class="result" id="result"></div>
    </div>

    <script>
        const conversionRates = {
            bar: 100000, // 1 巴 = 100000 帕
            kpa: 1000, // 1 千帕 = 1000 帕
            hpa: 100, // 1 百帕 = 100 帕
            mbar: 100, // 1 毫巴 = 100 帕
            pa: 1, // 1 帕 = 1 帕
            atm: 101325, // 1 标准大气压 = 101325 帕
            mmHg: 133.322, // 1 毫米汞柱 = 133.322 帕
            psi: 6894.76, // 1 磅力每平方英寸 = 6894.76 帕
            mmH2O: 9.80665, // 1 毫米水柱 = 9.80665 帕
            kgfcm2: 98066.5 // 1 公斤力每平方厘米 = 98066.5 帕
        };

        function convert() {
            const inputValue = parseFloat(document.getElementById('inputValue').value);
            const selectedUnit = document.getElementById('unitSelect').value;

            if (isNaN(inputValue)) {
                document.getElementById('result').innerText = '请输入有效的数值。';
                return;
            }

            const resultElement = document.getElementById('result');
            resultElement.innerHTML = ''; // 清空之前的结果

            const baseValue = inputValue * conversionRates[selectedUnit]; // 将输入值转换为帕斯卡

            const unitNames = {
                bar: '巴 (Bar)',
                kpa: '千帕 (kPa)',
                hpa: '百帕 (hPa)',
                mbar: '毫巴 (mbar)',
                pa: '帕斯卡 (Pa)',
                atm: '标准大气压 (atm)',
                mmHg: '毫米汞柱 (mmHg)',
                psi: '磅力每平方英寸 (psi)',
                mmH2O: '毫米水柱 (mmH2O)',
                kgfcm2: '公斤力每平方厘米 (kgf/cm²)'
            };

            for (const unit in conversionRates) {
                if (unit !== selectedUnit) { // 只显示与输入单位不同的转换结果
                    const convertedValue = baseValue / conversionRates[unit];
                    resultElement.innerHTML += `<p>${convertedValue.toFixed(6)} ${unitNames[unit]}</p>`;
                }
            }
        }
    </script>
</body>
</html>
