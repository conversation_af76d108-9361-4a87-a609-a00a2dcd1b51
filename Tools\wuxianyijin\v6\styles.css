body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

header {
    background-color: #333;
    color: #fff;
    padding: 10px 0;
    text-align: center;
}

header h1 {
    margin: 0;
    font-size: 24px;
}

main {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

main h2 {
    font-size: 22px;
    margin-bottom: 15px;
}

label {
    display: inline-block;
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 14px;
}

#salary {
    padding: 5px;
    font-size: 14px;
    width: 150px;
    margin-bottom: 15px;
}

#rate-inputs {
    margin-bottom: 15px;
    background-color: #f9f9f9;
    padding: 10px;
    border-radius: 5px;
}

#rate-inputs h3 {
    margin-bottom: 10px;
    font-size: 16px;
}

#rate-inputs label {
    display: inline-block;
    width: 200px;
    font-size: 14px;
}

#rate-inputs input {
    padding: 5px;
    font-size: 14px;
    width: 100px;
    margin-bottom: 10px;
}

button {
    padding: 8px 12px;
    background-color: #007bff;
    color: #fff;
    border: none;
    cursor: pointer;
    font-size: 14px;
    border-radius: 5px;
}

button:hover {
    background-color: #0056b3;
}

#result {
    margin-top: 20px;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
    font-size: 14px;
}

table th {
    background-color: #f4f4f4;
    font-weight: bold;
}

table td {
    background-color: #fff;
}

footer {
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 10px 0;
    position: fixed;
    width: 100%;
    bottom: 0;
}
