
# 前言  

<p>
许多法规、质量标准和公司政策都要求对计算机化分析系统进行验证。本手册指导质量保证 （QA）、信息技术 （IT） 和实验室管理人员以及计算机控制分析仪器的用户完成整个验证过程，从编写验证计划和设置规范到实施、测试和安装认证，再到持续校准、性能认证和变更控制。对于从供应商处购买的设备，它提供了有关用户和供应商的验证责任以及供应商资格的指南。它还就如何回顾性地评估和验证现有计算机系统以及如何验证用于实验室应用的“办公软件”（例如电子表格和数据库）以及用户编写的软件提供了指南，以进一步定制从供应商处购买的系统。

</p>

这本书源于一本成功的验证参考书 （Huber 1995）。超过 50% 的内容是新的。原书中与计算机不是 100% 相关的内容，例如，有关设备定性和分析方法验证的详细信息，已被取出并包含在题为“分析实验室的验证和确认”（Huber 1998）的补充书中。相反，本书包括有关网络鉴定和网络系统验证的章节。最近，由于美国食品和药物管理局 （FDA） 的 21 CodeofFederalRegulations （CFR） Part11 单记录和签名，关于电子记录和电子签名性质的完整性和安全性进行了许多讨论。预计这些讨论将继续进行;因此，有一章专门讨论这个主题。

本书考虑了 FDA 法规和指南，以及大多数国家和国际法规和质量标准，例如 ISO/IEC 17025。本书的概念以及包含的示例和模板基于作者在 Agilent Technologies 内部验证各个方面的跨国经验。此外，还与监管机构、设备用户、企业 QA经理和验证小组、仪器供应商和顾问进行了多次个人讨论，并参加了作者举办的数百次世界各地的研讨会。本书的读者将学习如何加快他们的验证过程，第一次就做对，从而避免麻烦的返工，并获得对审计和检查的信心。

