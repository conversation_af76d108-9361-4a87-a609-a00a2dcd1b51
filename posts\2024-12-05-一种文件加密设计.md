# 一种文件加密设计

A technical exploration and guide

## Content



# 一种文件加密设计

### 需求回顾：

1. **文件名保密**：你需要对文件名进行随机化，使其不能通过名称识别出文件内容或顺序。
2. **映射表记录**：所有的原始文件名和重命名后的文件名必须在一个映射表中完整记录，并且保留播放顺序。
3. **分组信息**：需要通过映射表来表明这些文件属于同一组，方便管理。

### 详细操作步骤的回顾和改进：

#### 1. **获取原始文件并排序**：

- 遍历目标文件夹，读取所有文件名，并解析出文件名中的数字序号以确保顺序正确。
- 如果文件名中带有固定格式（如“测试文件0001.txt”），可以提取出其中的数字部分进行排序，这样在重命名后，依然可以通过映射表还原正确顺序。

#### 2. **生成随机文件名**：

- 随机文件名的生成方式可以基于字母和数字的组合，长度可以控制在一定范围（如16字符或更多），确保无法通过文件名推断出内容或顺序。
- 示例文件名可以是 `fk23432_2343.txt` 或 `zxmnm823mnvcn.txt`，其中：
  - **字母**部分随机生成，不具有任何实际意义。
  - **数字**部分也随机生成，进一步增加不可预测性。
- 文件扩展名保持原样，以确保文件格式不变。

#### 3. **生成并保存映射表**：

- 映射表应包含以下信息：

  1. **原文件名**：记录原始的文件名，便于追溯。
  2. **新文件名**：记录每个文件重命名后的随机名称。
  3. **顺序信息**：保留文件的顺序，确保播放或使用时能够按原顺序进行。
  4. **组信息**：在映射表中增加一列，用来标识文件的组。例如，所有文件属于“Group1”。

  映射表的示例可以是 CSV 格式，内容如下：

```
   组名, 原文件名, 新文件名, 顺序
   Group1, 测试文件0001.txt, fk23432_2343.txt, 1
   Group1, 测试文件0002.txt, zxmnm823mnvcn.txt, 2
   Group1, 测试文件0003.txt, qwerty09876.txt, 3
   ...
   Group1, 测试文件0068.txt, abcdefghi123.txt, 68
```

   **关键要点**：

- **顺序字段**：通过顺序字段，你可以确保即便文件名已被打乱，仍然可以根据映射表按原顺序处理这些文件。
- **组字段**：表明这组文件是一起的，方便管理。

#### 4. **文件重命名**：

- 使用映射表中的新文件名，对文件逐个进行实际重命名。
- 确保文件的扩展名保持不变。
- 文件重命名完成后，目录中的文件名应完全随机化且无规律可循。

#### 5. **验证和安全性**：

- **映射表的安全性**：因为映射表包含了原文件名与新文件名的对应关系，它是所有文件的核心。如果映射表丢失或泄露，保密性将受到影响。因此：

  - **加密映射表**：可以对映射表进行加密存储，确保只有授权用户能够读取它。
  - **备份映射表**：将映射表妥善备份，确保其不会意外丢失。
  - **访问控制**：限制映射表的访问权限，确保只有你或授权人员能够读取和更新它。
- **重命名验证**：在重命名之后，可以通过映射表对文件逐一验证，确保所有文件都已正确重命名，并且顺序没有错乱。

#### 6. **进一步优化的建议**：

- **文件名冲突检测**：确保生成的随机文件名不会与已有文件名冲突。如果存在潜在的冲突情况，可以在生成文件名时增加冲突检测机制，避免重名。
- **程序化自动化**：你可以通过脚本（如 Python、Lua 等）来自动完成上述流程，确保过程高效、准确，并减少人为错误。

#### 示例流程图：

```
获取文件 -> 解析文件名顺序 -> 生成随机文件名 -> 更新映射表 -> 执行重命名 -> 验证和安全管理
```

总结：

你现在有一套完整的逻辑来管理这批文件：

- **文件名的完全随机化**确保保密性。
- **映射表**作为文件顺序和原始名称的唯一桥梁，确保保留文件的本质和顺序。
- **分组信息**便于将这些文件作为一个整体来管理。

### 对文件进行哈希处理

1. **完整性验证**：

   - **哈希值**可以作为每个文件的唯一标识符，确保文件在重命名、传输或存储过程中没有被篡改或损坏。
   - 在你重命名文件后，哈希值可以帮助你验证文件是否与原始文件一致，防止因重命名或其他操作导致文件内容意外发生变化。
2. **防篡改性**：

   - 哈希值使得任何对文件的改动（例如文件内容的修改）都能被快速检测到。即使文件名已经改变，如果内容发生任何变化，哈希值也会随之改变，你可以通过检查哈希值确认文件是否遭到篡改。
3. **文件的唯一标识**：

   - 哈希值可以作为文件的指纹，即使文件名被更改，文件的哈希值依然唯一，能够帮助识别重复文件或检测文件的冲突。
4. **进一步保护隐私**：

   - 哈希值可以让你在映射表中进一步隐藏文件的内容。即便其他人获得了映射表，他们也无法通过哈希值推断出文件的实际内容，但你依然可以通过哈希值验证文件的真实性和一致性。

#### 何时进行哈希处理：

你可以在文件重命名之前或之后对文件进行哈希处理，具体操作取决于需求：

1. **重命名前的哈希处理**：这样可以确保哈希值与原始文件一致，你可以使用哈希值来验证原始文件是否被更改。
2. **重命名后的哈希处理**：如果你希望验证重命名后的文件是否与原文件一致，重命名后进行哈希计算也可以。

#### 记录哈希值的映射表结构：

映射表中可以增加一列哈希值，记录文件的哈希值，形成如下结构：

```
组名, 原文件名, 新文件名, 顺序, 哈希值
Group1, 测试文件0001.txt, fk23432_2343.txt, 1, 3d20e355a2b9ed3fd1621fd3b6a83572
Group1, 测试文件0002.txt, zxmnm823mnvcn.txt, 2, d3486ae9136e7856bc42212385ea7970
...
Group1, 测试文件0068.txt, abcdefghi123.txt, 68, aab3238922bcc25a6f606eb525ffdc56
```

#### 使用哈希算法：

常见的哈希算法有：

- **MD5**：生成较短的哈希值，速度快，但安全性较弱（可以通过碰撞攻击破解）。
- **SHA-256**：生成更长且更安全的哈希值，适用于需要高安全性的场景。

你可以根据需求选择不同的哈希算法来计算文件的哈希值。

#### 是否有必要使用哈希值：

- 如果你希望进一步确保文件在重命名和管理过程中不会被篡改，或是需要对文件完整性进行验证，**哈希值是非常有用的**。
- 如果你只关心文件名的保密性，而不关心文件内容的完整性或防篡改性，那么哈希处理可能不是绝对必要的。

#### 总结：

对文件进行哈希处理并记录在映射表中，可以提升文件管理过程中的**安全性**和**完整性验证**。如果你认为文件的内容完整性和篡改防护是重要的，这个步骤是非常值得的。

你可以根据实际需求选择是否进行哈希处理。如果你决定执行哈希操作，后续我们可以讨论如何集成到你现有的流程中。

# Demo

### 映射表的字段：

1. **组名**：表示这些文件属于同一组，比如 Group1。
2. **原文件名**：文件的原始名称，记录文件的实际内容和来源。
3. **新文件名**：随机生成的文件名，确保无法通过文件名推测内容或顺序。
4. **文件顺序**：文件在播放或处理时的顺序，用于保持原来的顺序。
5. **哈希值**：通过哈希算法生成的唯一值，确保文件内容没有被篡改。常用的哈希算法包括 MD5、SHA-256 等。
6. **加密密钥/加密信息**：用于记录文件加密时使用的密钥或算法。如果你使用的是对称加密（如 AES），这里可以记录加密密钥。如果使用的是非对称加密（如 RSA），可以记录公钥/私钥信息。
7. **分块信息**（如果文件被切分）：用于记录文件被分块后的块号及每个块的加密信息，确保还原时能够正确组装文件。
8. **文件类型/扩展名**：如果你使用了自定义文件类型或隐藏了文件的扩展名，可以记录原始的文件类型，确保解密后能够正确识别文件类型。

### 示例映射表：

| 组名   | 原文件名         | 新文件名          | 文件顺序 | 哈希值                               | 加密密钥/加密信息 | 分块信息            | 文件类型/扩展名 |
| ------ | ---------------- | ----------------- | -------- | ------------------------------------ | ----------------- | ------------------- | --------------- |
| Group1 | 测试文件0001.txt | fk23432_2343.enc  | 1        | 3d20e355a2b9ed3fd1621fd3b6a83572     | AES Key: abc123   | 无                  | .txt            |
| Group1 | 测试文件0002.txt | zxmnm823mnvcn.enc | 2        | d3486ae9136e7856bc42212385ea7970     | AES Key: abc123   | 无                  | .txt            |
| Group1 | 测试文件0003.txt | qwerty09876.enc   | 3        | 9e107d9d372bb6826bd81d3542a419d6     | AES Key: abc123   | 无                  | .txt            |
| Group1 | 测试文件0004.txt | abcdefghi123.enc  | 4        | e4da3b7fbbce2345d7772b0674a318d5     | AES Key: abc123   | 块1, 块2 (加密信息) | .txt            |
| Group1 | 测试文件0005.jpg | pqrstuv12345.enc  | 5        | f7c3bc1d808e04732adf679965ccc34ca7ae | RSA Key: 2048-bit | 无                  | .jpg            |

### 字段解释：

1. **组名**：每个文件可以属于一个特定的组，便于管理和区分不同类型的文件。
2. **原文件名**：记录文件的原始名称，便于追溯文件来源和内容。
3. **新文件名**：生成的随机文件名，并带有扩展名 `.enc` 表示该文件已被加密。
4. **文件顺序**：记录文件的原始顺序，确保在需要时能够按正确的顺序播放或处理文件。
5. **哈希值**：用于验证文件完整性，确保文件内容未被篡改。每个文件的哈希值是唯一的，即使文件名改变，哈希值也不会变。
6. **加密密钥/加密信息**：如果文件是通过对称加密方式加密的，可以记录加密密钥。如果是非对称加密，可以记录公钥/私钥的相关信息。
7. **分块信息**：如果文件被分割为多个块并加密，这里记录每个块的序号以及相应的加密信息，以确保在恢复时能够正确拼装文件。
8. **文件类型/扩展名**：记录文件的原始类型，以便在解密和还原时能够正确识别文件格式。

### 总结：

这张映射表囊括了所有必要的信息，使得即便文件名和内容被加密并随机化，仍然能够通过映射表恢复文件的顺序、类型和内容。你可以根据需求将这些信息存储在 CSV 文件或数据库中，以确保管理和查询的便捷性。如果文件块数量较多，最好使用一个数据库来进行高效管理。

## 关于分块的解释

因为**测试文件0004.txt**经过了分块处理，被分割为多个加密块，因此在文件系统中它将显示为多个独立的文件块。如果你将每个分块存储为单独的文件，那么实际上整个文件系统中将多出几个文件。

### 解释：

- **测试文件0004.txt**在映射表中记录为分块的文件，即“块1”和“块2”。这意味着文件被拆分为两个块，并且每个块单独进行了加密。
- 这样处理后，文件系统中会有两个独立的文件块，分别对应于“块1”和“块2”。

### 显示在文件系统中的情况：

假设你对“测试文件0004.txt”进行了分块并保存为两个加密块，那么在文件系统中实际显示的将是 **6个文件**，其中“测试文件0004.txt”将对应2个文件块。例如：

```
/文件块文件夹/
    fk23432_2343.enc      (测试文件0001的加密文件)
    zxmnm823mnvcn.enc     (测试文件0002的加密文件)
    qwerty09876.enc       (测试文件0003的加密文件)
    abcdefghi123_part1.enc  (测试文件0004的加密块1)
    abcdefghi123_part2.enc  (测试文件0004的加密块2)
    pqrstuv12345.enc      (测试文件0005的加密文件)
```

### 补充映射表：

在映射表中，分块的信息也应反映在每个文件的记录上。具体到“测试文件0004.txt”的情况，映射表可能如下：

| 组名   | 原文件名         | 新文件名               | 文件顺序 | 哈希值                               | 加密密钥/加密信息 | 分块信息           | 文件类型/扩展名 |
| ------ | ---------------- | ---------------------- | -------- | ------------------------------------ | ----------------- | ------------------ | --------------- |
| Group1 | 测试文件0001.txt | fk23432_2343.enc       | 1        | 3d20e355a2b9ed3fd1621fd3b6a83572     | AES Key: abc123   | 无                 | .txt            |
| Group1 | 测试文件0002.txt | zxmnm823mnvcn.enc      | 2        | d3486ae9136e7856bc42212385ea7970     | AES Key: abc123   | 无                 | .txt            |
| Group1 | 测试文件0003.txt | qwerty09876.enc        | 3        | 9e107d9d372bb6826bd81d3542a419d6     | AES Key: abc123   | 无                 | .txt            |
| Group1 | 测试文件0004.txt | abcdefghi123_part1.enc | 4        | e4da3b7fbbce2345d7772b0674a318d5     | AES Key: abc123   | 块1，分块大小: 1MB | .txt            |
| Group1 | 测试文件0004.txt | abcdefghi123_part2.enc | 4        | e4da3b7fbbce2345d7772b0674a318d5     | AES Key: abc123   | 块2，分块大小: 1MB | .txt            |
| Group1 | 测试文件0005.jpg | pqrstuv12345.enc       | 5        | f7c3bc1d808e04732adf679965ccc34ca7ae | RSA Key: 2048-bit | 无                 | .jpg            |

### 总结：

- **文件系统中增加的文件数**：由于文件被分块，原本一个文件（如“测试文件0004.txt”）现在在文件系统中显示为两个独立的文件块，即 `abcdefghi123_part1.enc` 和 `abcdefghi123_part2.enc`。
- **映射表记录**：映射表中需要反映每个文件的分块信息，确保能够正确恢复文件。
- **文件总数**：原本 5 个文件，由于分块，现在文件系统中显示为 6 个文件。

当你将文件加密并生成随机文件名后，这些**文件块**在文件系统中的表现形式与普通文件没有本质上的区别，它们依然是操作系统可以识别的文件，但其文件名、扩展名和内容都已经被混淆或加密，使得外人无法直接通过名称或内容了解文件的实际用途。

## 文件块在文件系统中的表现：

1. **文件名称**：

   - 你已经将文件的名称随机化，比如 `fk23432_2343.enc`、`zxmnm823mnvcn.enc` 等，这些名称看似无规律，外人无法通过文件名推断出任何有用信息。
   - 文件名可能不再反映文件的真实内容或格式（如 `.txt`、`.jpg` 等），而是你自行定义的随机命名方式和扩展名（如 `.enc` 表示已加密）。
2. **文件内容**：

   - 文件的内容已经经过加密处理，因此在普通的文本编辑器或查看工具中会显示为无意义的乱码或二进制数据。
   - 即使有人获取了这些文件，打开后也只会看到加密后的数据流，无法获取文件的原始信息。
3. **文件大小**：

   - 加密后的文件大小通常会稍微大于原文件大小（取决于加密算法），但对于分块加密的文件，每个文件块的大小可以由你自行控制。
   - 如果你对文件进行了分块处理，文件在文件系统中可能会分为多个部分，每个部分代表原文件的一个加密块。

### 文件系统中的结构举例：

假设你有一个包含多个加密文件块的文件夹，结构可能如下所示：

```
/文件块文件夹/
    fk23432_2343.enc      (已加密文件块)
    zxmnm823mnvcn.enc     (已加密文件块)
    abcdefghi123.enc      (已加密文件块)
    pqrstuv12345.enc      (已加密文件块)
    ...
```

### 文件在文件系统中的可见性：

1. **普通用户/外部用户的视角**：

   - 外人打开文件夹时，只能看到随机名称的文件，并且文件的扩展名是你自定义的（如 `.enc`）。
   - 外人即使尝试打开这些文件，看到的也是加密后的数据，无法直接获取任何有意义的信息。
   - 文件名、扩展名和内容无法反映文件的实际内容或结构。
2. **通过映射表恢复文件**：

   - 只有通过你生成的映射表，才能将这些随机文件名与原文件名对应起来，并通过正确的解密密钥还原文件的真实内容。
   - 文件的顺序、分组、原始名称以及类型信息都依赖于映射表来恢复。

### 文件块的操作性：

- **正常操作**：你依然可以对这些文件执行常规操作，如复制、移动、删除等。文件系统会把它们当作普通文件对待。
- **传输安全性**：由于文件已经被加密，即便在传输过程中被截获，也无法直接读取内容。
- **进一步伪装**：你还可以通过修改文件的扩展名（如 `.bin`、`.dat`）来进一步伪装这些文件，使它们看起来像是无关的数据文件，甚至使用隐写术将文件隐藏在图片或其他媒体文件中。

### 总结：

在文件系统中，加密后的文件块表现为一组随机命名的文件，外人无法通过名称或内容识别出它们的实际用途或顺序。你需要依靠映射表和加密密钥来恢复文件的原始内容和顺序。整个过程确保了文件的安全性和隐私保护。
