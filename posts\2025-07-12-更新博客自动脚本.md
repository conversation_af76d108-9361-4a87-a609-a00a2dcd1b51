# 📝 博客自动化工作流程

  

## 🎯 使用方式

  

现在您只需要3个简单步骤：

  

### 1. 📄 编写文章

- 在 `posts/` 目录中创建或编辑 `.md` 文件

- 文件名建议格式：`YYYY-MM-DD-标题.md` （日期会自动提取）

- 如果不按日期格式命名也没关系，脚本会使用文件创建时间

  

### 2. 🚀 运行脚本

```bash

python generate_index.py

```

  

### 3. ✅ 完成！

- 脚本会自动扫描所有 `.md` 文件

- 自动生成/更新 `posts/index.md` 和 `posts/index.json`

- 自动提交到 Git 并推送到 GitHub

- 您的博客立即可用！

  

## 🛠️ 脚本功能

  

### ✨ 全自动功能

- **智能扫描**：自动发现 `posts/` 目录中的所有 `.md` 文件

- **标题提取**：从文件内容的 `# 标题` 或文件名自动提取标题

- **日期识别**：从文件名 `YYYY-MM-DD-` 格式或文件创建时间提取日期

- **增量更新**：只处理新增或变更的文件，保留现有条目

- **自动排序**：按日期降序排列文章

- **Git集成**：自动提交和推送更改

  

### 🎛️ 命令选项

```bash

# 默认模式：全自动处理

python generate_index.py

  

# 禁用Git操作（仅本地更新）

python generate_index.py --no-git

  

# 传统模式（需要手动维护index.md）

python generate_index.py --legacy

  

# 查看帮助

python generate_index.py --help

```

  

## 📊 脚本输出示例

  

```

🚀 Starting automatic blog index generation...

==================================================

📊 Analysis:

  - Total MD files: 16

  - Existing entries: 15

  - New files: 1 ['2024-12-05-新文章.md']

  - Missing files: 0 []

  

✅ Updated posts/index.md

✅ Updated posts/index.json

✅ Git operations completed: Auto-update blog: added 1 post(s)

  

🎉 Blog index generation completed successfully!

📝 Total posts: 16

🆕 New posts added: 2024-12-05-新文章.md

```

  

## 🔧 配置选项

  

在 `generate_index.py` 顶部可以修改：

  

```python

# 默认作者

DEFAULT_AUTHOR = "Lifu"

  

# 默认许可证

DEFAULT_LICENSE = "MIT"

  

# 排除的文件（不作为博客文章）

EXCLUDED_FILES = {'index.md', 'about.md'}

  

# Git自动提交开关

GIT_ENABLED = True

```

  

## 🎨 文章元数据

  

脚本会自动为每篇文章生成基本元数据：

- **title**: 从文件内容或文件名提取

- **date**: 从文件名或创建时间提取

- **file**: 文件名

- **author**: 使用默认作者

  

如需更多元数据（excerpt、version、license），可以手动编辑生成的 `posts/index.md`。

  

## 🚨 注意事项

  

1. **备份重要**：首次使用前建议备份现有的 `posts/index.md`

2. **Git仓库**：确保在Git仓库中运行，否则会跳过Git操作

3. **文件编码**：确保 `.md` 文件使用 UTF-8 编码

4. **文件名**：避免使用特殊字符，建议使用英文和数字

  

## 🎉 优势

  

- **零配置**：添加文章后直接运行脚本即可

- **智能化**：自动处理标题、日期、排序等

- **增量式**：只处理变更，不影响现有条目

- **Git集成**：一键发布到GitHub Pages

- **向后兼容**：保留原有的手动模式

  

现在您可以专注于写作，技术细节交给脚本处理！ 🎯