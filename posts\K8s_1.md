# K8s 相关问题集锦（一）

### 1. **基础概念类**

- **Kubernetes 是什么？它的主要功能有哪些？**
  - 说明 Kubernetes 是一个用于容器编排的开源平台，支持自动化容器化应用的部署、扩展和管理。
- **解释 Pod 是什么？**
  - Pod 是 Kubernetes 中最小的可部署单元，通常包含一个或多个容器，它们共享网络命名空间和存储卷。
- **什么是 Namespace？它的作用是什么？**
  - Namespace 用于逻辑上划分 Kubernetes 集群中的资源，可以用于实现资源隔离和管理不同环境（如开发、测试、生产）。
- **什么是 Deployment？它有什么作用？**
  - Deployment 用于管理无状态应用的部署，支持自动扩展、滚动更新和版本回滚。


转发一个快速入门视频

<iframe width="560" height="315" src="https://www.youtube.com/embed/ubz3cFgxeJA?si=gDu3Edwu_wNfDGYf" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>




### 2. **架构与组件类**

- **Kubernetes 的主要组件有哪些？**
  - 描述 Master 节点组件（如 API Server、Controller Manager、Scheduler、etcd）和 Worker 节点组件（如 kubelet、kube-proxy）。
- **解释 API Server 的作用是什么？**
  - API Server 是 Kubernetes 的前端，负责处理来自用户和内部组件的所有 RESTful 请求，是集群的核心入口。
- **什么是 etcd？在 Kubernetes 中起什么作用？**
  - etcd 是一个分布式键值存储系统，用于存储 Kubernetes 的集群数据，如配置信息、状态数据等，是 Kubernetes 高可用的重要组成部分。

### 3. **配置与操作类**

> 好的，以下是基于 Debian 操作系统的具体 Kubernetes 操作命令示例，涵盖创建 Pod、查看日志、排查 `CrashLoopBackOff` 状态、以及创建和使用 `ConfigMap` 和 `Secret` 的操作：
>
> ### 1. **如何创建一个 Pod？**
>
> 在 Kubernetes 中，我们可以使用两种方式创建 Pod：`kubectl run` 命令和 `kubectl apply -f` 命令。
>
> #### 方式一：使用 `kubectl run` 命令
>
> ```bash
> kubectl run nginx-pod --image=nginx:latest --restart=Never
> ```
>
> - `nginx-pod` 是 Pod 的名称。
> - `--image=nginx:latest` 指定使用的镜像。
> - `--restart=Never` 表示仅创建一个 Pod，而不是创建 Deployment 或 ReplicaSet。
>
> #### 方式二：使用 YAML 文件创建 Pod
>
> 创建一个名为 `nginx-pod.yaml` 的文件，内容如下：
>
> ```yaml
> apiVersion: v1
> kind: Pod
> metadata:
>   name: nginx-pod
> spec:
>   containers:
>   - name: nginx
>     image: nginx:latest
>     ports:
>     - containerPort: 80
> ```
>
> 然后使用以下命令应用这个配置文件来创建 Pod：
>
> ```bash
> kubectl apply -f nginx-pod.yaml
> ```
>
> ### 2. **如何查看 Pod 的日志？**
>
> 假设我们要查看名为 `nginx-pod` 的 Pod 的日志，可以使用以下命令：
>
> ```bash
> kubectl logs nginx-pod
> ```
>
> - 如果 Pod 中有多个容器，可以使用以下命令指定容器名：
>
> ```bash
> kubectl logs nginx-pod -c <container_name>
> ```
>
> ### 3. **如何排查 Pod 处于 `CrashLoopBackOff` 状态的问题？**
>
> 当 Pod 进入 `CrashLoopBackOff` 状态时，排查步骤包括查看事件、描述 Pod 详细信息和查看容器日志。
>
> #### 步骤 1：查看 Pod 的描述
>
> ```bash
> kubectl describe pod nginx-pod
> ```
>
> - 这个命令会输出 Pod 的详细信息，包括事件历史和错误原因。检查 `Events` 部分，可以帮助理解为什么 Pod 会频繁重启。
>
> #### 步骤 2：查看 Pod 日志
>
> ```bash
> kubectl logs nginx-pod
> ```
>
> - 如果 Pod 中有多个容器，可以使用以下命令指定容器名：
>
> ```bash
> kubectl logs nginx-pod -c <container_name>
> ```
>
> - 日志可以帮助查看应用程序启动失败的原因。
>
> #### 步骤 3：检查 Pod 的配置文件或镜像
>
> - 确认 YAML 文件中的配置是否正确，如环境变量、镜像名称、启动命令等。
> - 确保镜像没有问题，可以通过在本地或其他环境中测试镜像。
>
> ### 4. **如何创建和使用 `ConfigMap` 和 `Secret`？**
>
> #### 创建 `ConfigMap`
>
> 假设我们要创建一个包含应用配置的 `ConfigMap`：
>
> ```bash
> kubectl create configmap my-config --from-literal=app_name=myapp --from-literal=app_env=production
> ```
>
> - `--from-literal` 用于从命令行直接指定键值对。
> - 可以使用 `--from-file` 参数从文件加载配置。
>
> 查看 `ConfigMap` 的内容：
>
> ```bash
> kubectl get configmap my-config -o yaml
> ```
>
> #### 创建 `Secret`
>
> 假设我们要创建一个包含数据库密码的 `Secret`：
>
> ```bash
> kubectl create secret generic db-secret --from-literal=username=myuser --from-literal=password=mypassword
> ```
>
> - `generic` 表示创建的是一个通用类型的 Secret。
> - `--from-literal` 用于从命令行直接指定键值对。
>
> 查看 `Secret` 的内容（注意，`Secret` 的内容是 base64 编码的）：
>
> ```bash
> kubectl get secret db-secret -o yaml
> ```
>
> #### 将 `ConfigMap` 和 `Secret` 挂载到 Pod 中
>
> 可以将 `ConfigMap` 和 `Secret` 挂载为环境变量或文件。
>
> 创建一个名为 `nginx-pod-with-config.yaml` 的文件：
>
> ```yaml
> apiVersion: v1
> kind: Pod
> metadata:
>   name: nginx-pod-with-config
> spec:
>   containers:
>   - name: nginx
>     image: nginx:latest
>     env:
>     - name: APP_NAME
>       valueFrom:
>         configMapKeyRef:
>           name: my-config
>           key: app_name
>     - name: APP_ENV
>       valueFrom:
>         configMapKeyRef:
>           name: my-config
>           key: app_env
>     - name: DB_USERNAME
>       valueFrom:
>         secretKeyRef:
>           name: db-secret
>           key: username
>     - name: DB_PASSWORD
>       valueFrom:
>         secretKeyRef:
>           name: db-secret
>           key: password
> ```
>
> 应用这个 YAML 文件：
>
> ```bash
> kubectl apply -f nginx-pod-with-config.yaml
> ```
>
> - 这样创建的 Pod 将可以从 `ConfigMap` 和 `Secret` 中读取环境变量。
> - 可以在容器中通过 `echo $APP_NAME` 等命令查看这些环境变量的值。

### 4. **网络与存储类**

- **Kubernetes 中的 Service 是什么？有哪几种类型？**
  - Service 用于暴露应用，类型包括 ClusterIP、NodePort、LoadBalancer、ExternalName。
- **解释 Ingress 是什么？如何配置 Ingress？**
  - Ingress 是一个 API 对象，用于管理外部访问到集群服务的 HTTP 和 HTTPS 路由。
- **Kubernetes 中如何实现持久存储？**
  - 使用 PersistentVolume (PV) 和 PersistentVolumeClaim (PVC)，以及 StorageClass 管理持久存储。
- **ClusterIP 和 NodePort 有什么区别？**
  - ClusterIP 只在集群内部暴露服务，而 NodePort 则通过每个节点的端口将服务暴露给集群外部。
