# Kubernetes 的目录结构

## Master节点

在 Kubernetes 集群中，`master` 节点（即控制平面）是负责集群管理的核心。控制平面通常包括一些关键组件，如 `kube-apiserver`、`kube-scheduler`、`kube-controller-manager`、`etcd` 等。Kubernetes 的主要配置文件和数据文件在 master 节点上分布在不同的目录中，下面详细介绍这些目录结构和配置文件的位置。

Kubernetes Master 节点上的主要配置文件和目录可以概括如下：

- **控制平面组件的静态 Pod 配置**：`/etc/kubernetes/manifests/`
- **证书与密钥**：`/etc/kubernetes/pki/`
- **集群访问配置**：`/etc/kubernetes/admin.conf`
- **kubelet 服务配置**：`/etc/systemd/system/kubelet.service.d/`
- **etcd 数据存储**：`/var/lib/etcd/`
- **CNI 插件配置**：`/etc/cni/net.d/` 和 `/opt/cni/bin/`
- **日志文件**：`/var/log/`

### 一、Kubernetes Master 节点上的主要目录结构

1. **`/etc/kubernetes`**：

   - **`/etc/kubernetes/manifests`**：存放 Kubernetes 控制平面组件的静态 Pod 配置文件。例如，`kube-apiserver.yaml`、`kube-scheduler.yaml`、`kube-controller-manager.yaml` 等。通过静态 Pod 方式来启动和管理这些组件。
   - **`/etc/kubernetes/pki`**：存放 Kubernetes 集群中使用的证书和密钥文件，包括 API 服务器、etcd 等组件的 CA 证书和其他密钥。
   - **`/etc/kubernetes/admin.conf`**：集群的管理员配置文件，用于 `kubectl` 命令与集群交互。其他节点也可能有类似的 `kubelet.conf`、`controller-manager.conf`、`scheduler.conf` 等配置文件。
2. **`/var/lib/kubelet`**：

   - 存放 kubelet 组件的配置和数据。kubelet 是负责在每个节点上管理 Pod 的核心组件。
3. **`/var/lib/etcd`**：

   - etcd 是 Kubernetes 的数据存储组件，用于存储集群的状态和配置信息。该目录存放 etcd 的数据文件，是整个集群的核心数据存储。
4. **`/etc/systemd/system/kubelet.service.d`**：

   - 用于存放 `kubelet` 服务的配置文件，通常在这里找到一个 `10-kubeadm.conf` 文件，用于设置 kubelet 启动时的参数，例如 API 服务器地址等。
5. **`/opt/cni/bin`**** 和 **`/etc/cni/net.d`**：

   - **`/opt/cni/bin`**：存放网络插件的可执行文件，如 Flannel、Calico、Weave 等。
   - **`/etc/cni/net.d`**：存放网络插件的配置文件，例如 `10-flannel.conflist` 或 `calico.conflist`。
6. **`/var/log`**：

   - 该目录中存放了各个组件的日志文件，例如 kubelet、kube-apiserver、kube-controller-manager、etcd 的日志。具体日志文件视具体的系统配置而定，通常日志被存储在 `/var/log` 下，或者通过 systemd journal 管理。

### 二、Master 节点上的主要配置文件位置及作用

1. **控制平面组件的静态 Pod 配置文件**：

   - **路径**：`/etc/kubernetes/manifests/`
   - **文件**：
     - `kube-apiserver.yaml`：API 服务器的静态 Pod 配置文件。
     - `kube-scheduler.yaml`：调度器的静态 Pod 配置文件。
     - `kube-controller-manager.yaml`：控制器管理器的静态 Pod 配置文件。
     - `etcd.yaml`：etcd 数据库的静态 Pod 配置文件。
2. **Kubernetes 证书与密钥**：

   - **路径**：`/etc/kubernetes/pki/`
   - **文件**：
     - `ca.crt` 和 `ca.key`：CA 证书和密钥。
     - `apiserver.crt` 和 `apiserver.key`：API 服务器的证书和密钥。
     - `etcd/`：etcd 组件的证书和密钥，通常位于 `pki` 下的子目录 `etcd` 中。
3. **集群访问配置文件**：

   - **路径**：`/etc/kubernetes/admin.conf`
   - **文件**：
     - `admin.conf`：集群管理员的配置文件，用于通过 `kubectl` 命令与集群通信。这个文件中包含 API 服务器的地址、证书、令牌等信息。
4. **kubelet 服务配置**：

   - **路径**：`/etc/systemd/system/kubelet.service.d/`
   - **文件**：
     - `10-kubeadm.conf`：用于配置 kubelet 服务的启动参数。此配置文件包括一些环境变量配置，例如 API 服务器地址、证书位置、网络参数等。
5. **etcd 数据存储**：

   - **路径**：`/var/lib/etcd/`
   - **文件**：
     - etcd 的数据文件和快照存放在这个目录中，用于存储 Kubernetes 集群的状态信息，是整个集群最为关键的数据存储位置。
6. **CNI 插件相关配置**：

   - **路径**：`/etc/cni/net.d/` 和 `/opt/cni/bin/`
   - **文件**：
     - 网络插件的配置文件（例如 flannel、calico），通常以 `.conf` 或 `.conflist` 结尾。
     - 网络插件的可执行文件（例如 CNI 插件的二进制文件）。
7. **日志文件**：

   - **路径**：`/var/log/`
   - **文件**：
     - `kube-apiserver.log`、`kube-scheduler.log`、`kube-controller-manager.log` 等，存放了这些组件的运行日志，便于管理员进行调试和故障排查。



## Worker节点

在 Kubernetes 集群中，`worker` 节点（也叫计算节点或工作节点）是负责运行容器化应用的节点，主要托管实际的 Pod，并执行控制平面下发的任务。Worker 节点上也有许多配置文件和目录结构，下面我来详细介绍。

在 Kubernetes 的 Worker 节点上，主要的目录结构和配置文件如下：

- **kubelet 配置文件**：`/etc/kubernetes/kubelet.conf`，用于 kubelet 与 API 服务器通信。
- **kubelet 服务配置**：`/etc/systemd/system/kubelet.service.d/10-kubeadm.conf`，用于 kubelet 启动参数的配置。
- **证书和密钥文件**：`/etc/kubernetes/pki/`，存放 kubelet 的证书和密钥。
- **CNI 插件相关配置**：`/opt/cni/bin/` 和 `/etc/cni/net.d/`，用于 Pod 的网络配置。
- **kubelet 数据目录**：`/var/lib/kubelet`，存放 kubelet 相关的运行数据。
- **容器运行时数据目录**：`/var/lib/docker`（或 `/var/lib/containerd`），存放容器镜像和运行数据。
- **日志文件**：`/var/log/kubelet.log` 和 `/var/log/containers/`，用于记录节点和 Pod 的运行日志。

### 一、Worker 节点的主要目录结构

1. **`/etc/kubernetes`**：

   - **`/etc/kubernetes/kubelet.conf`**：kubelet 配置文件，worker 节点的 kubelet 使用该文件与集群 API 服务器通信。
   - **`/etc/kubernetes/pki`**：存放用于 kubelet 与 API 服务器通信的证书和密钥，例如 kubelet 的 CA 证书。
2. **`/var/lib/kubelet`**：

   - kubelet 是 worker 节点的核心组件之一，负责管理节点上的容器。该目录包含 kubelet 的数据、配置和管理 Pod 所需的状态文件。
   - **`/var/lib/kubelet/pods`**：存储与节点上运行的 Pod 相关的数据，包括 Pod 的配置信息、运行状态和临时数据。
3. **`/etc/systemd/system/kubelet.service.d`**：

   - **`10-kubeadm.conf`**：用于配置 kubelet 的启动参数和一些重要的环境变量，如 API 服务器地址、证书路径等。
4. **`/opt/cni/bin`** 和 **`/etc/cni/net.d`**：

   - **`/opt/cni/bin`**：存放 CNI 网络插件的可执行文件，例如 Flannel、Calico、Weave 等插件的可执行文件。
   - **`/etc/cni/net.d`**：存放 CNI 网络插件的配置文件，用于定义节点上 Pod 的网络连接方式，例如 `10-flannel.conflist` 或 `calico.conflist`。
5. **`/var/lib/docker`**（或其他容器运行时目录，如 containerd）：

   - 存放 Docker 容器（或其他容器运行时）相关的数据，包括容器镜像、容器配置等。
   - 如果使用 `containerd`，则目录为 `/var/lib/containerd`。
6. **`/var/log`**：

   - **`/var/log/kubelet.log`**：记录 kubelet 组件的运行日志信息，便于管理员排查节点上的问题。
   - **`/var/log/containers/`**：存放运行中的容器日志，每个容器都会有一个相应的日志文件，便于追踪应用的运行状态和排查问题。

### 二、Worker 节点的主要配置文件位置及作用

1. **kubelet 配置文件**：

   - **路径**：`/etc/kubernetes/kubelet.conf`
   - **作用**：
     - 该文件是 worker 节点上 kubelet 的配置文件，用于与 Kubernetes API 服务器通信。它包含了集群的 API 服务器地址、认证证书和授权信息等。
2. **kubelet 服务配置**：

   - **路径**：`/etc/systemd/system/kubelet.service.d/10-kubeadm.conf`
   - **作用**：
     - 用于设置 kubelet 的启动参数。包括了 kubelet 启动时需要的环境变量，如 `KUBELET_KUBECONFIG_ARGS`（kubelet 配置文件路径）、`KUBELET_CERTIFICATE_ARGS`（证书路径）等。
3. **证书和密钥文件**：

   - **路径**：`/etc/kubernetes/pki`
   - **作用**：
     - 存放用于身份认证的证书和密钥，如 kubelet 的 CA 证书（`ca.crt`），用于保证 kubelet 与 API 服务器之间通信的安全性。
4. **CNI 插件相关配置**：

   - **路径**：
     - **`/opt/cni/bin`**：存放 CNI 网络插件的可执行文件。
     - **`/etc/cni/net.d/`**：存放网络插件的配置文件，用于定义 Pod 的网络配置。
   - **作用**：
     - CNI 插件负责为 Pod 配置网络，使得集群内的 Pod 可以相互通信。
5. **kubelet 数据目录**：

   - **路径**：`/var/lib/kubelet`
   - **作用**：
     - kubelet 使用该目录来存储运行中的 Pod 的信息，包括每个 Pod 的状态、挂载的卷、网络信息等。
6. **容器运行时数据目录**：

   - **路径**：`/var/lib/docker` 或 `/var/lib/containerd`
   - **作用**：
     - 如果使用 Docker 作为容器运行时，则 `/var/lib/docker` 会包含所有容器镜像、容器实例的存储信息。
     - 如果使用 `containerd` 作为容器运行时，则相关数据存放在 `/var/lib/containerd` 目录中。
7. **日志文件**：

   - **路径**：
     - **`/var/log/kubelet.log`**：kubelet 的日志文件，记录 kubelet 在节点上运行的详细信息。
     - **`/var/log/containers/`**：每个 Pod 容器的日志文件，方便调试和跟踪应用的运行状态。
   - **作用**：
     - 日志文件对排查节点上的问题和应用运行时的问题非常有用。管理员可以通过这些日志了解 Pod 的运行情况和节点的状态。
