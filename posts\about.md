# About Me

Welcome to my personal blog! This space is where I share my thoughts, experiences, and knowledge about various topics that interest me.

## What I Write About

- Technology and Programming
- Personal Projects
- Tutorials and Guides
- And more...

## Contact

Feel free to reach out to me through:

- Email:<EMAIL>

## About This Blog

This blog is built with:

- HTML5 & CSS3
- JavaScript
- Showdown.js for Markdown rendering
- Hosted on GitHub Pages

The design is inspired by the monospace theme from [<PERSON><PERSON><PERSON>](https://owickstrom.github.io/the-monospace-web/), focusing on readability and simplicity.

## How to Use This Blog

### For Readers

- Navigate through posts using the pagination at the bottom of the home page
- Switch between light and dark mode based on your system preferences
- Click on any post title to read the full article
- Use the navigation menu to switch between different sections

### For Content Management

This blog features an automated post management system:

1. **Adding New Posts**
   - Create a new Markdown file in the `posts` directory
   - Use the naming format: `YYYY-MM-DD-title.md`
   - Optionally include YAML frontmatter for metadata:

     ```yaml
     ---
     title: Your Post Title
     date: YYYY-MM-DD
     excerpt: Brief description
     author: Your Name
     version: v0.1.1
     license: MIT
     ---
     ```

2. **Automatic Index Updates**
   - The blog uses GitHub Actions for automation
   - When you push new Markdown files, the index updates automatically
   - No manual JSON editing required
   - The system maintains the posts/index.json file

3. **Post Features**
   - Support for Markdown formatting
   - Code syntax highlighting
   - Image and video embedding
   - Responsive media display
   - Table support
   - Automatic date sorting

4. **Maintenance**
   - Posts are automatically sorted by date
   - The index is automatically updated
   - Mobile-responsive design adapts automatically
   - Dark/light mode switches based on system preferences